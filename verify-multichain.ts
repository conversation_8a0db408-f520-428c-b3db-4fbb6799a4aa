import { verifyHardhatDeployNonTarget } from "@layerzerolabs/verify-contract";
import dotenv from "dotenv";
dotenv.config();

async function main() {
    console.log("Starting verification for USDUC contracts...");

    await verifyHardhatDeployNonTarget({
        paths: {
            // Use our local deployments directory
            deployments: "./deployments",
        },
        networks: {
            "ethereum-mainnet": {
                apiUrl: "https://api.etherscan.io/api",
                apiKey: process.env.ETHERSCAN_API_KEY!,
            },
            "base-mainnet": {
                apiUrl: "https://api.basescan.org/api",
                apiKey: process.env.BASESCAN_API_KEY!,
            },
        },
        contracts: [
            {
                address: "******************************************",
                network: "ethereum-mainnet",
                deployment: "ethereum-mainnet/USDUC.json",
                constructorArguments: [
                    "unstable coin",
                    "USDUC",
                    "******************************************",
                    "******************************************",
                ],
                contractName: "contracts/MyOFT.sol:USDUC",
            },
            {
                address: "******************************************",
                network: "base-mainnet",
                deployment: "base-mainnet/USDUC.json",
                constructorArguments: [
                    "unstable coin",
                    "USDUC",
                    "******************************************",
                    "******************************************",
                ],
                contractName: "contracts/MyOFT.sol:USDUC",
            },
        ],
    });

    console.log("Verification submitted successfully!");
    console.log("Check the following URLs for verification status:");
    console.log("Ethereum: https://etherscan.io/address/******************************************");
    console.log("Base: https://basescan.org/address/******************************************");
}

main().catch((err) => {
    console.error("Verification failed:", err);
    process.exit(1);
});
