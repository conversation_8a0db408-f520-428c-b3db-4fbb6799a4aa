import { verifyHardhatDeployNonTarget } from "layerzerolabs/verify-contract";
import dotenv from "dotenv";
dotenv.config();

async function main() {
    await verifyHardhatDeployNonTarget({
        paths: {
            // This can be anything — we just need the ABI from it.
            deployments: "./node_modules/@layerzerolabs/create3-factory/deployments",
        },
        networks: {
            "ethereum-mainnet": {
                apiUrl: "https://api.etherscan.io/api",
                apiKey: process.env.VERIFY_API_KEY!,
            },
            "base-mainnet": {
                apiUrl: "https://api.basescan.org/api",
                apiKey: process.env.VERIFY_API_KEY!,
            },
        },
        contracts: [
            {
                address: "******************************************",
                network: "ethereum-mainnet",
                deployment: "ethereum-mainnet/CREATE3Factory.json",
                constructorArguments: [
                    "unstable coin",
                    "USDUC",
                    "******************************************",
                    "******************************************",
                ],
                contractName: "contracts/MyOFT.sol:USDUC",
            },
            {
                address: "******************************************",
                network: "base-mainnet",
                deployment: "base-mainnet/CREATE3Factory.json",
                constructorArguments: [
                    "unstable coin",
                    "USDUC",
                    "******************************************",
                    "******************************************",
                ],
                contractName: "contracts/MyOFT.sol:USDUC",
            },
        ],
    });

    console.log("Verification submitted.");
}

main().catch((err) => {
    console.error("Verification failed:", err);
    process.exit(1);
});
