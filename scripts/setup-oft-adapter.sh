#!/bin/bash

# Setup script for OFT-Adapter with Solana as origin chain and deterministic EVM deployments

set -e

echo "🚀 Setting up OFT-Adapter with Create3 deterministic deployments"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️  .env file not found. Please copy .env.example to .env and fill in your values.${NC}"
    echo "Required environment variables:"
    echo "  - PRIVATE_KEY or MNEMONIC (for EVM deployments)"
    echo "  - SOLANA_PRIVATE_KEY or SOLANA_KEYPAIR_PATH (for Solana deployments)"
    echo "  - RPC URLs (optional, defaults provided)"
    exit 1
fi

echo -e "${BLUE}📋 Step 1: Compile contracts${NC}"
pnpm run compile

echo -e "${BLUE}📋 Step 2: Predict deterministic addresses${NC}"
SALT="ooRooy1wisoZah2ier1aejei4foht6xaigev]ie5aiJ1eikoaj{ee8queevoa9ti"
echo "Using salt: $SALT"

echo "Predicting address for Ethereum testnet..."
ETH_ADDRESS=$(pnpm hardhat create3:predict --network sepolia-testnet --salt "$SALT" | grep "Predicted address:" | cut -d' ' -f3)

echo "Predicting address for Base testnet..."
BASE_ADDRESS=$(pnpm hardhat create3:predict --network base-testnet --salt "$SALT" | grep "Predicted address:" | cut -d' ' -f3)

echo -e "${GREEN}Predicted addresses:${NC}"
echo "  Ethereum testnet: $ETH_ADDRESS"
echo "  Base testnet: $BASE_ADDRESS"

if [ "$ETH_ADDRESS" = "$BASE_ADDRESS" ]; then
    echo -e "${GREEN}✅ Addresses match! Deterministic deployment working correctly.${NC}"
else
    echo -e "${RED}❌ Addresses don't match! Check your setup.${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Step 3: Deploy MyOFT contracts using Create3${NC}"
pnpm hardhat create3:deploy-oft --salt "$SALT" --networks "sepolia-testnet,base-testnet"

echo -e "${BLUE}📋 Step 4: Verify addresses are consistent${NC}"
pnpm hardhat create3:verify-addresses --networks "sepolia-testnet,base-testnet"

echo -e "${BLUE}📋 Step 5: Create Solana OFT-Adapter${NC}"
echo -e "${YELLOW}⚠️  You need to manually create the Solana OFT-Adapter using:${NC}"
echo ""
echo "For existing token (OFT-Adapter - lock/unlock on Solana):"
echo "  pnpm hardhat lz:oft-adapter:solana:create --eid 40168 --program-id <PROGRAM_ID> --mint <TOKEN_MINT> --token-program <TOKEN_PROGRAM_ID>"
echo ""
echo "For new token (OFT - mint/burn on Solana):"
echo "  pnpm hardhat lz:oft:solana:create --eid 40168 --program-id <PROGRAM_ID> --mint <TOKEN_MINT> --token-program <TOKEN_PROGRAM_ID>"
echo ""
echo -e "${YELLOW}Replace <PROGRAM_ID>, <TOKEN_MINT>, and <TOKEN_PROGRAM_ID> with your actual values.${NC}"

echo -e "${BLUE}📋 Step 6: Configure LayerZero connections${NC}"
echo "After creating the Solana OFT-Adapter, run:"
echo "  pnpm hardhat lz:oapp:wire --oapp-config layerzero.config.ts"

echo -e "${GREEN}🎉 Setup complete!${NC}"
echo ""
echo -e "${BLUE}📝 Next steps:${NC}"
echo "1. Create your Solana OFT-Adapter using the command above"
echo "2. Wire the contracts using the LayerZero config"
echo "3. Test cross-chain transfers"
echo ""
echo -e "${BLUE}📊 Your deterministic address: ${GREEN}$ETH_ADDRESS${NC}"
echo "This address will be the same on all EVM chains when using the same salt."
echo ""
echo -e "${BLUE}🔗 Networks configured:${NC}"
echo "  - Solana Testnet (Origin - OFT-Adapter: lock/unlock)"
echo "  - Ethereum Testnet (Synthetic - OFT: mint/burn)"
echo "  - Base Testnet (Synthetic - OFT: mint/burn)"
