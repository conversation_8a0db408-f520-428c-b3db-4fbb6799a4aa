import { ethers } from 'hardhat';
import hre from 'hardhat';
import dotenv from 'dotenv';

dotenv.config();

interface ContractInfo {
    address: string;
    network: string;
    constructorArgs: any[];
    contractName: string;
    apiKey: string;
    apiUrl: string;
    explorerUrl: string;
}

const contracts: ContractInfo[] = [
    {
        address: "******************************************",
        network: "ethereum-mainnet",
        constructorArgs: [
            "unstable coin",
            "USDUC", 
            "******************************************",
            "******************************************"
        ],
        contractName: "contracts/MyOFT.sol:USDUC",
        apiKey: process.env.ETHERSCAN_API_KEY || "",
        apiUrl: "https://api.etherscan.io/api",
        explorerUrl: "https://etherscan.io"
    },
    {
        address: "******************************************",
        network: "base-mainnet",
        constructorArgs: [
            "unstable coin",
            "USDUC",
            "******************************************", 
            "******************************************"
        ],
        contractName: "contracts/MyOFT.sol:USDUC",
        apiKey: process.env.BASESCAN_API_KEY || "",
        apiUrl: "https://api.basescan.org/api",
        explorerUrl: "https://basescan.org"
    }
];

async function verifyContract(contractInfo: ContractInfo) {
    console.log(`\n🔍 Verifying ${contractInfo.contractName} on ${contractInfo.network}...`);
    console.log(`📍 Address: ${contractInfo.address}`);
    
    if (!contractInfo.apiKey) {
        console.error(`❌ API key not found for ${contractInfo.network}`);
        return false;
    }

    try {
        // Use Hardhat's verify task programmatically
        await hre.run("verify:verify", {
            address: contractInfo.address,
            constructorArguments: contractInfo.constructorArgs,
            contract: contractInfo.contractName,
            network: contractInfo.network
        });
        
        console.log(`✅ Successfully verified on ${contractInfo.network}`);
        console.log(`🔗 View at: ${contractInfo.explorerUrl}/address/${contractInfo.address}`);
        return true;
        
    } catch (error: any) {
        if (error.message.includes("Already Verified")) {
            console.log(`✅ Contract already verified on ${contractInfo.network}`);
            console.log(`🔗 View at: ${contractInfo.explorerUrl}/address/${contractInfo.address}`);
            return true;
        } else {
            console.error(`❌ Verification failed on ${contractInfo.network}:`, error.message);
            return false;
        }
    }
}

async function verifyWithEtherscan(contractInfo: ContractInfo) {
    console.log(`\n🔍 Verifying with direct Etherscan API call on ${contractInfo.network}...`);
    
    const params = new URLSearchParams({
        module: 'contract',
        action: 'verifysourcecode',
        apikey: contractInfo.apiKey,
        contractaddress: contractInfo.address,
        sourceCode: '', // Will be filled by compilation
        codeformat: 'solidity-standard-json-input',
        contractname: 'contracts/MyOFT.sol:USDUC',
        compilerversion: 'v0.8.22+commit.4fc1097e',
        optimizationUsed: '1',
        runs: '200',
        constructorArguements: ethers.utils.defaultAbiCoder.encode(
            ['string', 'string', 'address', 'address'],
            contractInfo.constructorArgs
        ).slice(2) // Remove 0x prefix
    });

    console.log(`📡 Making API call to ${contractInfo.apiUrl}`);
    console.log(`🔗 Will be viewable at: ${contractInfo.explorerUrl}/address/${contractInfo.address}`);
    
    // Note: This is a simplified version. For full implementation, you'd need to:
    // 1. Compile the contract to get the standard JSON input
    // 2. Make the actual HTTP request
    // 3. Poll for verification status
    
    console.log(`ℹ️  Manual verification parameters:`);
    console.log(`   Contract Address: ${contractInfo.address}`);
    console.log(`   Contract Name: USDUC`);
    console.log(`   Compiler Version: 0.8.22`);
    console.log(`   Optimization: Yes (200 runs)`);
    console.log(`   Constructor Args: ${contractInfo.constructorArgs.join(', ')}`);
}

async function main() {
    console.log("🚀 Starting contract verification process...");
    console.log("📋 Contracts to verify:");
    
    contracts.forEach((contract, index) => {
        console.log(`   ${index + 1}. ${contract.contractName} on ${contract.network}`);
        console.log(`      Address: ${contract.address}`);
    });

    let successCount = 0;
    
    for (const contractInfo of contracts) {
        try {
            const success = await verifyContract(contractInfo);
            if (success) successCount++;
            
            // Add delay between verifications to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`💥 Unexpected error verifying ${contractInfo.network}:`, error);
        }
    }
    
    console.log(`\n📊 Verification Summary:`);
    console.log(`   ✅ Successful: ${successCount}/${contracts.length}`);
    console.log(`   ❌ Failed: ${contracts.length - successCount}/${contracts.length}`);
    
    if (successCount === contracts.length) {
        console.log(`\n🎉 All contracts verified successfully!`);
    } else {
        console.log(`\n⚠️  Some verifications failed. Check the logs above for details.`);
        console.log(`\n💡 Troubleshooting tips:`);
        console.log(`   1. Ensure API keys are set in .env file:`);
        console.log(`      - ETHERSCAN_API_KEY=your_etherscan_api_key`);
        console.log(`      - BASESCAN_API_KEY=your_basescan_api_key`);
        console.log(`   2. Check that the contract addresses are correct`);
        console.log(`   3. Verify constructor arguments match deployment`);
        console.log(`   4. Ensure the contract source code hasn't changed`);
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Script failed:", error);
            process.exit(1);
        });
}

export { verifyContract, contracts };
