[profile.default]
solc-version = '0.8.22'
src = 'contracts'
out = 'out'
test = 'test/foundry'
cache_path = 'cache/foundry'
optimizer = true
optimizer_runs = 20_000

libs = [
    # We provide a set of useful contract utilities
    # in the lib directory of @layerzerolabs/toolbox-foundry:
    # 
    # - forge-std
    # - ds-test
    # - solidity-bytes-utils
    'node_modules/@layerzerolabs/toolbox-foundry/lib',
    'node_modules',
]

remappings = [
    # Due to a misconfiguration of solidity-bytes-utils, an outdated version
    # of forge-std is being dragged in
    # 
    # To remedy this, we'll remap the ds-test and forge-std imports to our own versions
    'ds-test/=node_modules/@layerzerolabs/toolbox-foundry/lib/ds-test',
    'forge-std/=node_modules/@layerzerolabs/toolbox-foundry/lib/forge-std',
    '@layerzerolabs/=node_modules/@layerzerolabs/',
    '@openzeppelin/=node_modules/@openzeppelin/',
]
