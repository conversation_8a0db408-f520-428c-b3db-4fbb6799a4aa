import { task } from 'hardhat/config'

const create3FactoryAddress = '0x8Cad6A96B0a287e29bA719257d0eF431Ea6D888B'

// CREATE3Factory ABI (minimal interface we need)
const CREATE3_FACTORY_ABI = [
    {
        "inputs": [
            { "internalType": "address", "name": "deployer", "type": "address" },
            { "internalType": "bytes32", "name": "salt", "type": "bytes32" }
        ],
        "name": "getDeployed",
        "outputs": [{ "internalType": "address", "name": "deployed", "type": "address" }],
        "stateMutability": "view",
        "type": "function"
    }
]

// Task to predict Create3 deployment address
task('create3:predict', 'Predict the Create3 deployment address')
    .addParam('salt', 'The salt for deterministic deployment')
    .addOptionalParam('deployer', 'The deployer address (defaults to first account)')
    .setAction(async ({ salt, deployer }, hre) => {
        const { ethers } = hre

        if (!deployer) {
            const accounts = await ethers.getSigners()
            deployer = accounts[0].address
        }

        console.log(`Deployer: ${deployer}`)
        console.log(`Salt: ${salt}`)

        try {
            const create3FactoryContract = await ethers.getContractAt(CREATE3_FACTORY_ABI, create3FactoryAddress)

            const saltHash = ethers.utils.keccak256(ethers.utils.toUtf8Bytes(salt))
            const predictedAddress = await create3FactoryContract.getDeployed(deployer, saltHash)

            console.log(`Predicted address: ${predictedAddress}`)

            // Check if already deployed
            const code = await ethers.provider.getCode(predictedAddress)
            console.log(`Already deployed: ${code !== '0x'}`)

            return predictedAddress
        } catch (error) {
            console.error('Error predicting address:', error)
            throw error
        }
    })

// Task to deploy MyOFT using Create3 across multiple networks
task('create3:deploy-oft', 'Deploy MyOFT using Create3 for deterministic addresses')
    .addOptionalParam('salt', 'The salt for deterministic deployment', 'phai/ch0eeFiep>ae3EGh8pheiniba:uKei&y+ai9oY_o8rie5pe7Vae9gohChoh')
    .addOptionalParam('networks', 'Comma-separated list of networks', 'sepolia-testnet,base-testnet')
    .setAction(async ({ salt, networks }, hre) => {
        const { ethers } = hre
        const networkList = networks.split(',').map((n: string) => n.trim())

        console.log(`Deploying MyOFT to networks: ${networkList.join(', ')}`)
        console.log(`Using salt: ${salt}`)

        const saltHash = ethers.utils.keccak256(ethers.utils.toUtf8Bytes(salt))
        console.log(`Salt hash: ${saltHash}`)

        console.log('')
        console.log('Note: To deploy to specific networks, run the following commands:')
        console.log('')

        for (const network of networkList) {
            console.log(`pnpm hardhat deploy --network ${network} --tags MyOFT,Create3`)
        }

        console.log('')
        console.log('This will deploy your MyOFT contract to the same deterministic address on all networks.')

        return true
    })

// Task to verify Create3 addresses are the same across networks
task('create3:verify-addresses', 'Verify that Create3 addresses are the same across networks')
    .addOptionalParam('networks', 'Comma-separated list of networks', 'sepolia-testnet,base-testnet')
    .addOptionalParam('salt', 'The salt for deterministic deployment', 'ooRooy1wisoZah2ier1aejei4foht6xaigev]ie5aiJ1eikoaj{ee8queevoa9ti')
    .setAction(async ({ networks, salt }, hre) => {
        const networkList = networks.split(',').map((n: string) => n.trim())

        console.log(`Verifying Create3 addresses across networks: ${networkList.join(', ')}`)
        console.log(`Using salt: ${salt}`)
        console.log('')

        // Note: This task just provides instructions since we can't easily switch networks in a single task
        console.log('To verify addresses match, run the following commands:')
        console.log('')

        for (const network of networkList) {
            console.log(`pnpm hardhat create3:predict --network ${network} --salt "${salt}"`)
        }

        console.log('')
        console.log('All predicted addresses should be identical for deterministic deployment to work.')

        return true
    })

export { }
