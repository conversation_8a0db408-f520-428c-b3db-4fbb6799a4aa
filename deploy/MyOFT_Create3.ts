import assert from 'assert'
import { type DeployFunction } from 'hardhat-deploy/types'

const contractName = 'USDUC'
const create3FactoryAddress = '******************************************'

const deploy: DeployFunction = async (hre) => {
    const { getNamedAccounts, deployments, ethers } = hre

    const { deployer } = await getNamedAccounts()

    assert(deployer, 'Missing named deployer account')

    console.log(`Network: ${hre.network.name}`)
    console.log(`Deployer: ${deployer}`)

    // Get the CREATE3Factory address for this network
    const chainId = await ethers.provider.getNetwork().then(n => n.chainId)

    console.log(`Using CREATE3Factory at: ${create3FactoryAddress}`)

    // CREATE3Factory ABI (minimal interface we need)
    const create3FactoryAbi = [
        {
            "inputs": [
                { "internalType": "bytes32", "name": "salt", "type": "bytes32" },
                { "internalType": "bytes", "name": "creationCode", "type": "bytes" }
            ],
            "name": "deploy",
            "outputs": [{ "internalType": "address", "name": "deployed", "type": "address" }],
            "stateMutability": "payable",
            "type": "function"
        },
        {
            "inputs": [
                { "internalType": "address", "name": "deployer", "type": "address" },
                { "internalType": "bytes32", "name": "salt", "type": "bytes32" }
            ],
            "name": "getDeployed",
            "outputs": [{ "internalType": "address", "name": "deployed", "type": "address" }],
            "stateMutability": "view",
            "type": "function"
        }
    ]

    // Get the LayerZero EndpointV2 address for this network
    const endpointV2Deployment = await hre.deployments.get('EndpointV2')
    const endpointV2Address = endpointV2Deployment.address

    if (!endpointV2Address) {
        throw new Error(`LayerZero EndpointV2 not available on chain ID ${chainId}`)
    }

    console.log(`Using LayerZero EndpointV2 at: ${endpointV2Address}`)

    // Define a deterministic salt for consistent addresses across chains
    // You can change this salt to get different addresses
    const salt = ethers.utils.keccak256(ethers.utils.toUtf8Bytes('phai/ch0eeFiep>ae3EGh8pheiniba:uKei&y+ai9oY_o8rie5pe7Vae9gohChoh'))

    // Encode constructor arguments
    const constructorArgs = [
        'unstable coin', // name
        'USDUC', // symbol
        endpointV2Address, // LayerZero's EndpointV2 address
        deployer, // owner
    ]

    // Get the contract factory
    const MyOFTFactory = await ethers.getContractFactory(contractName)
    const initCode = MyOFTFactory.getDeployTransaction(...constructorArgs).data

    if (!initCode) {
        throw new Error('Failed to generate init code')
    }

    // Calculate the deterministic address
    const create3FactoryContract = await ethers.getContractAt(create3FactoryAbi, create3FactoryAddress)
    const predictedAddress = await create3FactoryContract.getDeployed(deployer, salt)

    console.log(`Predicted address: ${predictedAddress}`)

    // Check if contract is already deployed at this address
    const code = await ethers.provider.getCode(predictedAddress)
    if (code !== '0x') {
        console.log(`Contract already deployed at ${predictedAddress}`)

        // Save the deployment info for hardhat-deploy
        await deployments.save(contractName, {
            address: predictedAddress,
            abi: MyOFTFactory.interface.format('json'),
            bytecode: MyOFTFactory.bytecode,
            deployedBytecode: code,
            args: constructorArgs,
        })

        return
    }

    // Estimate gas

    const gasEstimate = await create3FactoryContract.estimateGas.deploy(salt, initCode, { gasLimit: 5000000 })

    // Deploy using Create3
    console.log('Deploying with Create3...')

    const tx = await create3FactoryContract.deploy(salt, initCode, { gasLimit: gasEstimate.mul(12).div(10) })
    const receipt = await tx.wait()

    console.log(`Deployed contract: ${contractName}`)
    console.log(`Network: ${hre.network.name}`)
    console.log(`Address: ${predictedAddress}`)
    console.log(`Transaction hash: ${receipt.transactionHash}`)

    // Verify the deployment
    const deployedCode = await ethers.provider.getCode(predictedAddress)
    if (deployedCode === '0x') {
        throw new Error('Deployment failed - no code at predicted address')
    }

    // Save the deployment info for hardhat-deploy
    await deployments.save(contractName, {
        address: predictedAddress,
        abi: MyOFTFactory.interface.format('json'),
        bytecode: MyOFTFactory.bytecode,
        deployedBytecode: deployedCode,
        args: constructorArgs,
        transactionHash: receipt.transactionHash,
    })

    console.log(`Deployment saved for hardhat-deploy`)
}

deploy.tags = [contractName, 'Create3']
// No dependencies needed since we use the existing CREATE3Factory deployments

export default deploy
