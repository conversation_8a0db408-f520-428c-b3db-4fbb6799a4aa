# OFT-Adapter Setup with Create3 Deterministic Deployments

## 🎯 What We've Set Up

Your LayerZero OFT-Adapter is now configured for deterministic deployments across multiple chains with Solana as the origin chain:

### Architecture
- **Solana Testnet** (Origin): OFT-Adapter with lock/unlock mechanism
- **Ethereum Testnet** (Synthetic): OFT with mint/burn mechanism  
- **Base Testnet** (Synthetic): OFT with mint/burn mechanism

### Key Features
✅ **Deterministic Addresses**: Same contract address across all EVM chains  
✅ **Future-Proof**: Can deploy to new EVM chains with identical addresses  
✅ **Testnet Ready**: All configurations set for testnets  
✅ **OFT-Adapter Pattern**: Proper lock/unlock on Solana, mint/burn on EVMs  

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Copy and fill environment file
cp .env.example .env

# Add your keys to .env:
PRIVATE_KEY=your_evm_private_key
SOLANA_PRIVATE_KEY=your_solana_private_key_base58
```

### 2. One-Command Setup
```bash
pnpm run setup:oft-adapter
```

This script will:
- Compile all contracts
- Deploy Create3Factory to both EVM testnets
- Predict and verify deterministic addresses
- Deploy MyOFT contracts with same address on both chains
- Provide instructions for Solana setup

### 3. Create Solana OFT-Adapter
After the script completes, create your Solana OFT-Adapter:

```bash
# For existing token (recommended for origin chain)
pnpm hardhat lz:oft-adapter:solana:create \
  --eid 40168 \
  --program-id <YOUR_PROGRAM_ID> \
  --mint <YOUR_TOKEN_MINT> \
  --token-program <TOKEN_PROGRAM_ID>
```

### 4. Wire LayerZero Connections
```bash
pnpm hardhat lz:oapp:wire --oapp-config layerzero.config.ts
```

## 📁 Files Created/Modified

### New Files
- `deploy/MyOFT_Create3.ts` - Create3 deployment script
- `tasks/evm/create3Deploy.ts` - Create3 management tasks
- `scripts/setup-oft-adapter.sh` - Automated setup script
- `docs/CREATE3_SETUP.md` - Detailed setup guide

### Modified Files
- `hardhat.config.ts` - Added Ethereum and Base testnet networks
- `layerzero.config.ts` - Updated for testnets and added Base
- `package.json` - Added helpful npm scripts
- `.env.example` - Added new RPC URL examples
- `tasks/index.ts` - Imported Create3 tasks

## 🛠 Available Commands

### Deployment
```bash
pnpm run deploy:create3                    # Deploy with Create3
pnpm run predict:address                   # Predict deployment address
pnpm run verify:addresses                  # Verify addresses match
```

### Manual Tasks
```bash
# Predict address for specific network
pnpm hardhat create3:predict --network ethereum-testnet --salt "MyOFT-v1.0.0"

# Deploy to specific networks
pnpm hardhat create3:deploy-oft --networks "ethereum-testnet,base-testnet"

# Verify addresses across networks
pnpm hardhat create3:verify-addresses --networks "ethereum-testnet,base-testnet"
```

## 🔧 Configuration Details

### Network Configuration
```typescript
// hardhat.config.ts
networks: {
  'ethereum-testnet': {
    eid: EndpointId.ETHEREUM_V2_TESTNET,
    url: process.env.RPC_URL_ETHEREUM_TESTNET || 'https://gateway.tenderly.co/public/sepolia',
  },
  'base-testnet': {
    eid: EndpointId.BASE_V2_TESTNET,
    url: process.env.RPC_URL_BASE_TESTNET || 'https://sepolia.base.org',
  }
}
```

### LayerZero Configuration
```typescript
// layerzero.config.ts
const ethereumContract = { eid: EndpointId.ETHEREUM_V2_TESTNET, contractName: 'MyOFT' }
const baseContract = { eid: EndpointId.BASE_V2_TESTNET, contractName: 'MyOFT' }
const solanaContract = { eid: EndpointId.SOLANA_V2_TESTNET, address: getOftStoreAddress(...) }
```

## 🎯 Next Steps

1. **Set up your environment** by copying `.env.example` to `.env` and filling in your keys
2. **Run the setup script**: `pnpm run setup:oft-adapter`
3. **Create Solana OFT-Adapter** using the provided command
4. **Wire the contracts** using LayerZero config
5. **Test cross-chain transfers**

## 🔮 Future Expansion

To add new EVM chains:

1. Add network to `hardhat.config.ts`
2. Add contract config to `layerzero.config.ts`  
3. Deploy using same salt: `pnpm hardhat create3:deploy-oft --networks "new-network"`
4. The contract will have the **same address** as existing deployments!

## 📚 Documentation

- `docs/CREATE3_SETUP.md` - Detailed setup instructions
- `README.md` - Original LayerZero documentation
- `docs/wiring-to-aptos.md` - Example for other chains

## 🆘 Troubleshooting

### Common Issues
- **Address mismatch**: Ensure same deployer and salt across chains
- **Deployment fails**: Check private key and sufficient testnet ETH
- **Solana issues**: Verify keypair has SOL and correct program ID

### Getting Help
- Check the detailed setup guide in `docs/CREATE3_SETUP.md`
- Review LayerZero documentation
- Ensure all environment variables are set correctly

---

**Your deterministic address will be the same across all EVM chains! 🎉**
