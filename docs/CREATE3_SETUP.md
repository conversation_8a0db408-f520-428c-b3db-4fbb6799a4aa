# Create3 Deterministic Deployment Setup

This guide explains how to set up Create3 for deterministic cross-chain deployments of your OFT contracts with Solana as the origin chain.

## Overview

- **Solana Testnet**: Origin chain using OFT-Adapter (lock/unlock mechanism)
- **Ethereum Testnet**: Synthetic chain using OFT (mint/burn mechanism)  
- **Base Testnet**: Synthetic chain using OFT (mint/burn mechanism)

## Architecture

```
Solana (Origin)           Ethereum (Synthetic)      Base (Synthetic)
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│   OFT-Adapter   │◄────►│      MyOFT      │      │      MyOFT      │
│  (Lock/Unlock)  │      │  (Mint/Burn)    │◄────►│  (Mint/Burn)    │
└─────────────────┘      └─────────────────┘      └─────────────────┘
        │                         │                         │
        │                         │                         │
    Same Address              Same Address              Same Address
   (Solana format)           (0x123...abc)             (0x123...abc)
```

## Quick Setup

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Fill in your environment variables:**
   ```bash
   # Required for EVM deployments
   PRIVATE_KEY=your_private_key_here
   # OR
   MNEMONIC=your_mnemonic_here
   
   # Required for Solana deployments
   SOLANA_PRIVATE_KEY=your_solana_private_key_base58
   # OR
   SOLANA_KEYPAIR_PATH=path/to/your/keypair.json
   ```

3. **Run the setup script:**
   ```bash
   ./scripts/setup-oft-adapter.sh
   ```

## Manual Setup Steps

### 1. Compile Contracts
```bash
pnpm run compile
```

### 2. Deploy Create3Factory
```bash
# Deploy to Ethereum testnet
pnpm hardhat deploy --network ethereum-testnet --tags Create3Factory

# Deploy to Base testnet  
pnpm hardhat deploy --network base-testnet --tags Create3Factory
```

### 3. Predict Deterministic Addresses
```bash
# Check what address will be used
pnpm hardhat create3:predict --network ethereum-testnet --salt "MyOFT-v1.0.0"
pnpm hardhat create3:predict --network base-testnet --salt "MyOFT-v1.0.0"
```

### 4. Deploy MyOFT Contracts
```bash
# Deploy to both networks with same address
pnpm hardhat create3:deploy-oft --salt "MyOFT-v1.0.0" --networks "ethereum-testnet,base-testnet"
```

### 5. Verify Addresses Match
```bash
pnpm hardhat create3:verify-addresses --networks "ethereum-testnet,base-testnet"
```

### 6. Create Solana OFT-Adapter

For existing token (recommended for origin chain):
```bash
pnpm hardhat lz:oft-adapter:solana:create \
  --eid 40168 \
  --program-id <YOUR_PROGRAM_ID> \
  --mint <YOUR_TOKEN_MINT> \
  --token-program <TOKEN_PROGRAM_ID>
```

For new token:
```bash
pnpm hardhat lz:oft:solana:create \
  --eid 40168 \
  --program-id <YOUR_PROGRAM_ID> \
  --mint <YOUR_TOKEN_MINT> \
  --token-program <TOKEN_PROGRAM_ID>
```

### 7. Wire LayerZero Connections
```bash
pnpm hardhat lz:oapp:wire --oapp-config layerzero.config.ts
```

## Configuration Files

### Networks (hardhat.config.ts)
- `ethereum-testnet`: Ethereum Sepolia testnet
- `base-testnet`: Base Sepolia testnet

### LayerZero Config (layerzero.config.ts)
- Configured for Solana testnet as origin
- Ethereum and Base testnets as synthetic chains
- Proper enforced options for each chain type

## Key Benefits

1. **Deterministic Addresses**: Same contract address across all EVM chains
2. **Future-Proof**: Can deploy to new EVM chains with same address
3. **OFT-Adapter Pattern**: Solana locks/unlocks, EVMs mint/burn
4. **Testnet Ready**: All configurations set for testnets

## Troubleshooting

### Address Mismatch
If addresses don't match across chains:
- Ensure same deployer address on all chains
- Verify same salt is being used
- Check Create3Factory is deployed correctly

### Deployment Fails
- Check your private key/mnemonic is correct
- Ensure sufficient gas/ETH on testnet
- Verify RPC URLs are working

### Solana Issues
- Ensure Solana keypair has sufficient SOL
- Check program ID is correct
- Verify token mint exists (for OFT-Adapter)

## Advanced Usage

### Custom Salt
```bash
pnpm hardhat create3:deploy-oft --salt "MyCustomSalt-v2.0.0"
```

### Additional Networks
Add new networks to `hardhat.config.ts` and deploy:
```bash
pnpm hardhat create3:deploy-oft --networks "ethereum-testnet,base-testnet,arbitrum-testnet"
```

### Check Deployment Status
```bash
pnpm hardhat create3:verify-addresses --networks "ethereum-testnet,base-testnet"
```
