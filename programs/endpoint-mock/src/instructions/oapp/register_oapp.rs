use crate::*;

use cpi_helper::CpiContext;

#[event_cpi]
#[derive(CpiContext, Accounts)]
#[instruction(params: RegisterOAppParams)]
pub struct RegisterOApp<'info> {
    #[account(mut)]
    pub payer: Signer<'info>,
    /// The PDA of the OApp
    pub oapp: Signer<'info>,
    #[account(
        init,
        payer = payer,
        space = 8 + OAppRegistry::INIT_SPACE,
        seeds = [OAPP_SEED, oapp.key.as_ref()],
        bump
    )]
    pub oapp_registry: Account<'info, OAppRegistry>,
    pub system_program: Program<'info, System>,
}

impl RegisterOApp<'_> {
    pub fn apply(ctx: &mut Context<RegisterOApp>, params: &RegisterOAppParams) -> Result<()> {
        ctx.accounts.oapp_registry.delegate = params.delegate;
        ctx.accounts.oapp_registry.bump = ctx.bumps.oapp_registry;
        Ok(())
    }
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>erialize, AnchorDeserialize)]
pub struct RegisterOAppParams {
    pub delegate: Pub<PERSON>,
}
