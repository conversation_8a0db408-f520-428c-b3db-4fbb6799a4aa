{"name": "@layerzerolabs/oft-solana-example", "version": "0.11.1", "private": true, "scripts": {"clean": "rm -rf target artifacts cache out .anchor", "compile": "concurrently -c auto --names forge,hardhat,anchor '$npm_execpath run compile:forge' '$npm_execpath run compile:hardhat' '$npm_execpath run compile:anchor'", "compile:anchor": "anchor build", "compile:forge": "forge build", "compile:hardhat": "hardhat compile", "lint": "$npm_execpath run lint:js && $npm_execpath run lint:sol", "lint:fix": "eslint --fix '**/*.{js,ts,json}' && prettier --write . && solhint 'contracts/**/*.sol' --fix --noPrompt", "lint:js": "eslint '**/*.{js,ts,json}' && prettier --check .", "lint:sol": "solhint 'contracts/**/*.sol'", "test": "$npm_execpath run test:forge && $npm_execpath run test:hardhat", "test:anchor": "anchor test", "test:forge": "forge test", "test:hardhat": "hardhat test", "setup:oft-adapter": "./scripts/setup-oft-adapter.sh", "deploy:create3": "hardhat create3:deploy-oft", "predict:address": "hardhat create3:predict --salt ooRooy1wisoZah2ier1aejei4foht6xaigev]ie5aiJ1eikoaj{ee8queevoa9ti", "verify:addresses": "hardhat create3:verify-addresses --networks sepolia-testnet,base-testnet"}, "resolutions": {"@solana/web3.js": "^1.98.0", "ethers": "^5.7.2", "hardhat-deploy": "^0.12.1"}, "devDependencies": {"@coral-xyz/anchor": "^0.29.0", "@ethersproject/bytes": "^5.7.0", "@layerzerolabs/devtools": "~1.0.0", "@layerzerolabs/devtools-evm": "~2.0.0", "@layerzerolabs/devtools-evm-hardhat": "^3.1.0", "@layerzerolabs/devtools-solana": "~2.1.0", "@layerzerolabs/eslint-config-next": "~2.3.39", "@layerzerolabs/io-devtools": "~0.2.0", "@layerzerolabs/lz-definitions": "^3.0.86", "@layerzerolabs/lz-evm-messagelib-v2": "^3.0.86", "@layerzerolabs/lz-evm-protocol-v2": "^3.0.86", "@layerzerolabs/lz-evm-v1-0.7": "^3.0.86", "@layerzerolabs/lz-solana-sdk-v2": "^3.0.86", "@layerzerolabs/lz-v2-utilities": "^3.0.86", "@layerzerolabs/metadata-tools": "^2.0.0", "@layerzerolabs/oapp-evm": "^0.3.2", "@layerzerolabs/oft-evm": "^3.1.3", "@layerzerolabs/oft-v2-solana-sdk": "^3.0.86", "@layerzerolabs/prettier-config-next": "^2.3.39", "@layerzerolabs/protocol-devtools": "^2.0.0", "@layerzerolabs/protocol-devtools-evm": "~4.0.0", "@layerzerolabs/protocol-devtools-solana": "^7.0.0", "@layerzerolabs/solhint-config": "^3.0.12", "@layerzerolabs/test-devtools-evm-foundry": "~6.0.3", "@layerzerolabs/test-devtools-evm-hardhat": "~0.5.2", "@layerzerolabs/toolbox-foundry": "~0.1.12", "@layerzerolabs/toolbox-hardhat": "~0.6.11", "@layerzerolabs/ua-devtools": "~4.0.0", "@layerzerolabs/ua-devtools-evm": "~6.0.0", "@layerzerolabs/ua-devtools-evm-hardhat": "~8.0.0", "@layerzerolabs/ua-devtools-solana": "~7.0.0", "@metaplex-foundation/mpl-token-metadata": "^3.2.1", "@metaplex-foundation/mpl-toolbox": "^0.9.4", "@metaplex-foundation/umi": "^0.9.2", "@metaplex-foundation/umi-bundle-defaults": "^0.9.2", "@metaplex-foundation/umi-eddsa-web3js": "^0.9.2", "@metaplex-foundation/umi-public-keys": "^0.8.9", "@metaplex-foundation/umi-web3js-adapters": "^0.9.2", "@nomicfoundation/hardhat-ethers": "^3.0.5", "@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-waffle": "^2.0.6", "@openzeppelin/contracts": "^5.0.2", "@openzeppelin/contracts-upgradeable": "^5.0.2", "@rushstack/eslint-patch": "^1.7.0", "@solana-developers/helpers": "~2.8.1", "@solana/spl-token": "^0.4.8", "@solana/web3.js": "~1.95.8", "@sqds/sdk": "^2.0.4", "@swc/core": "^1.4.0", "@swc/jest": "^0.2.36", "@types/chai": "^4.3.11", "@types/jest": "^29.5.12", "@types/mocha": "^10.0.6", "@types/node": "~18.18.14", "bs58": "^6.0.0", "chai": "^4.4.1", "concurrently": "~9.1.0", "dotenv": "^16.4.5", "eslint": "^8.55.0", "eslint-plugin-jest-extended": "~2.0.0", "ethereumjs-util": "^7.1.5", "ethers": "^5.7.2", "exponential-backoff": "~3.1.1", "fp-ts": "^2.16.2", "hardhat": "^2.22.10", "hardhat-contract-sizer": "^2.10.0", "hardhat-deploy": "^0.12.1", "hardhat-deploy-ethers": "^0.4.2", "jest": "^29.7.0", "mocha": "^10.2.0", "prettier": "^3.2.5", "solhint": "^4.1.1", "solidity-bytes-utils": "^0.8.2", "ts-node": "^10.9.2", "typescript": "^5.4.4"}, "engines": {"node": ">=18.16.0"}, "pnpm": {"overrides": {"@solana/web3.js": "^1.98.0", "ethers": "^5.7.2", "hardhat-deploy": "^0.12.1"}}, "overrides": {"ethers": "^5.7.2", "hardhat-deploy": "^0.12.1", "@solana/web3.js": "^1.98.0"}, "dependencies": {"@layerzerolabs/create3-factory": "^3.0.111", "@layerzerolabs/verify-contract": "^1.1.33"}}