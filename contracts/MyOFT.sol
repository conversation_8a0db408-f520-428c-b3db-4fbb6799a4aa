// SPDX-License-Identifier: UNLICENSED

/*

https://usduc.xyz/
https://x.com/usduc_coin

you buy it before you go to sleep and you don’t know if you’ll be millionaire or hobo by the time you wake up

*/

pragma solidity ^0.8.22;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";
import { OFT } from "@layerzerolabs/oft-evm/contracts/OFT.sol";

contract USDUC is OFT {
    constructor(
        string memory _name,
        string memory _symbol,
        address _lzEndpoint,
        address _delegate
    ) OFT(_name, _symbol, _lzEndpoint, _delegate) Ownable(_delegate) {}
}
