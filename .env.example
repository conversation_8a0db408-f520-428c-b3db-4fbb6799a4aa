#   .-.-.   .-.-.   .-.-.   .-.-.   .-.-.   .-.-.   .-.-.   .-.-
#  / / \ \ / / \ \ / / \ \ / / \ \ / / \ \ / / \ \ / / \ \ / / \
# `-'   `-`-'   `-`-'   `-`-'   `-`-'   `-`-'   `-`-'   `-`-'
#
#               Example environment configuration
#
#   .-.-.   .-.-.   .-.-.   .-.-.   .-.-.   .-.-.   .-.-.   .-.-
#  / / \ \ / / \ \ / / \ \ / / \ \ / / \ \ / / \ \ / / \ \ / / \
# `-'   `-`-'   `-`-'   `-`-'   `-`-'   `-`-'   `-`-'   `-`-'

# By default, the examples support both mnemonic-based and private key-based authentication
#
# You don't need to set both of these values, just pick the one that you prefer and set that one
#
# By default, the Solana example will use the default cluster RPC URL if no other value is provided
# For SOLANA_PRIVATE_KEY use base58 encoding
MNEMONIC=
PRIVATE_KEY= # Private key for EVM contract owner/delegate
SOLANA_PRIVATE_KEY=
SOLANA_KEYPAIR_PATH=
RPC_URL_SOLANA=
RPC_URL_SOLANA_TESTNET=https://devnet.helius-rpc.com/?api-key=c83da65b-b753-44da-86b5-b0c1534539fa
RPC_URL_ETHEREUM_TESTNET=https://gateway.tenderly.co/public/sepolia
RPC_URL_BASE_TESTNET=https://sepolia.base.org
RPC_URL_SEPOLIA=https://gateway.tenderly.co/public/sepolia